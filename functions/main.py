from firebase_admin import initialize_app, auth as admin_auth
from firebase_functions import https_fn
from firebase_functions.https_fn import Request, Response
from openai import OpenAI
import os
import json
import requests
from urllib.parse import quote
import spoonacular
from spoonacular.rest import ApiException

# Initialize Firebase Admin SDK (for Firestore/Auth/etc. if needed)
initialize_app()

@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=300)
def generate(req: Request) -> Response:
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    decoded_token = admin_auth.verify_id_token(id_token)
    uid = decoded_token["uid"]

    try:
        data = req.get_json(silent=True)

        if not data or "input" not in data:
            return Response(json.dumps({"error": "Missing 'input' in request body"}), status=400, headers={"Content-Type": "application/json"})

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(json.dumps({"error": "OpenAI API key not found"}), status=500, headers={"Content-Type": "application/json"})

        client = OpenAI(api_key=api_key)

        # Create parameters dictionary with required fields
        params = {
            "model": data.get("model", "gpt-4.1"),
            "input": data["input"],
            "temperature": data.get("temperature", 1),
            "instructions": data.get("instructions"),
            "text": data.get("text", {"format": {"type": "text"}}),
            "user": uid
        }

        # Add previous_response_id if it exists in the request
        if "previous_response_id" in data and data["previous_response_id"]:
            params["previous_response_id"] = data["previous_response_id"]

        response = client.responses.create(**params)

        return Response(json.dumps(response.model_dump()), status=200, headers={"Content-Type": "application/json"})

    except Exception as e:
        return Response(json.dumps({"error": str(e)}), status=500, headers={"Content-Type": "application/json"})


@https_fn.on_request(secrets=["UNSPLASH_ACCESS_KEY"], timeout_sec=10)
def get_unsplash_image(req: Request) -> Response:
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "GET":
        return Response(
            json.dumps({"error": "Method not allowed. Only GET requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        query = req.args.get("query")

        if not query:
            return Response(
                json.dumps({"error": "Missing 'query' parameter"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Unsplash API key from secrets
        unsplash_key = os.environ.get("UNSPLASH_ACCESS_KEY")
        if not unsplash_key:
            return Response(
                json.dumps({"error": "Unsplash API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Call Unsplash API
        url = f"https://api.unsplash.com/search/photos?query={quote(query)}&per_page=1"
        headers = {
            "Authorization": f"Client-ID {unsplash_key}",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            return Response(
                json.dumps({
                    "error": f"Unsplash API returned status {response.status_code}",
                    "details": response.text
                }),
                status=response.status_code,
                headers={"Content-Type": "application/json"}
            )

        data = response.json()
        results = data.get("results", [])

        if not results:
            return Response(
                json.dumps({"error": "No images found for the given query"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        image_url = results[0].get("urls", {}).get("regular")

        if not image_url:
            return Response(
                json.dumps({"error": "Image URL not found in API response"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        return Response(
            json.dumps({"imageUrl": image_url}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except requests.exceptions.Timeout:
        return Response(
            json.dumps({"error": "Request to Unsplash API timed out"}),
            status=408,
            headers={"Content-Type": "application/json"}
        )
    except requests.exceptions.RequestException as e:
        return Response(
            json.dumps({"error": f"Network error: {str(e)}"}),
            status=503,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


@https_fn.on_request(secrets=["SPOONACULAR_API_KEY"], timeout_sec=60)
def search_spoonacular_recipes(req: Request) -> Response:
    """
    Search for recipes using Spoonacular API with search queries, diet preferences, and intolerances. Defaults to 10 results per query.

    Expected request body:
    {
        "searchQueries": ["pasta", "chicken curry", "vegetable stir fry"],
        "diet": "vegetarian",  // optional
        "intolerances": "gluten,dairy",  // optional, comma-separated
    }
    """
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "POST":
        return Response(
            json.dumps({"error": "Method not allowed. Only POST requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        data = req.get_json(silent=True)

        if not data:
            return Response(
                json.dumps({"error": "Missing request body"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        search_queries = data.get("searchQueries", [])
        if not search_queries or not isinstance(search_queries, list):
            return Response(
                json.dumps({"error": "Missing or invalid 'searchQueries' array"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Spoonacular API key from secrets
        spoonacular_key = os.environ.get("SPOONACULAR_API_KEY")
        if not spoonacular_key:
            return Response(
                json.dumps({"error": "Spoonacular API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Configure Spoonacular client
        configuration = spoonacular.Configuration(
            host="https://api.spoonacular.com"
        )
        configuration.api_key['apiKeyScheme'] = spoonacular_key

        all_recipes = []

        # Process each search query
        with spoonacular.ApiClient(configuration) as api_client:
            api_instance = spoonacular.RecipesApi(api_client)

            for query in search_queries:
                try:
                    # Prepare search parameters
                    search_params = {
                        'query': query,
                        'add_recipe_information': True,
                        'fill_ingredients': True,
                        'instructions_required': True
                    }

                    # Add diet preference if provided
                    if data.get("diet"):
                        search_params['diet'] = data["diet"]

                    # Add intolerances if provided
                    if data.get("intolerances"):
                        search_params['intolerances'] = data["intolerances"]

                    # Call Spoonacular API
                    response = api_instance.search_recipes(**search_params)

                    # Process the response
                    if hasattr(response, 'results') and response.results:
                        for recipe in response.results:
                            # Extract recipe information
                            recipe_data = {
                                "id": recipe.id,
                                "title": recipe.title,
                                "image": recipe.image if hasattr(recipe, 'image') else None,
                                "readyInMinutes": recipe.ready_in_minutes if hasattr(recipe, 'ready_in_minutes') else None,
                                "servings": recipe.servings if hasattr(recipe, 'servings') else None,
                                "sourceUrl": recipe.source_url if hasattr(recipe, 'source_url') else None,
                                "searchQuery": query,
                                "ingredients": [],
                                "instructions": ""
                            }

                            # Extract ingredients
                            if hasattr(recipe, 'extended_ingredients') and recipe.extended_ingredients:
                                recipe_data["ingredients"] = [
                                    {
                                        "name": ingredient.name,
                                        "amount": ingredient.amount if hasattr(ingredient, 'amount') else None,
                                        "unit": ingredient.unit if hasattr(ingredient, 'unit') else None,
                                        "original": ingredient.original if hasattr(ingredient, 'original') else None
                                    }
                                    for ingredient in recipe.extended_ingredients
                                ]

                            # Extract instructions
                            if hasattr(recipe, 'instructions') and recipe.instructions:
                                # Instructions can be a string or a list of instruction objects
                                if isinstance(recipe.instructions, str):
                                    recipe_data["instructions"] = recipe.instructions
                                elif isinstance(recipe.instructions, list) and len(recipe.instructions) > 0:
                                    # If it's a list of instruction objects, extract the steps
                                    instruction_text = ""
                                    for instruction_group in recipe.instructions:
                                        if hasattr(instruction_group, 'steps') and instruction_group.steps:
                                            for step in instruction_group.steps:
                                                if hasattr(step, 'step'):
                                                    instruction_text += f"{step.number}. {step.step}\n" if hasattr(step, 'number') else f"{step.step}\n"
                                    recipe_data["instructions"] = instruction_text.strip()

                            # Extract summary if available
                            if hasattr(recipe, 'summary'):
                                recipe_data["summary"] = recipe.summary

                            all_recipes.append(recipe_data)

                except ApiException as e:
                    print(f"Spoonacular API error for query '{query}': {e}")
                    continue
                except Exception as e:
                    print(f"Error processing query '{query}': {e}")
                    continue

        return Response(
            json.dumps({
                "recipes": all_recipes,
                "totalFound": len(all_recipes)
            }),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )